"use client";

import { Bad<PERSON> } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@ui/components/tabs";
import { ExternalLinkIcon, GlobeIcon, SaveIcon } from "lucide-react";
import { useState } from "react";
import { WebsiteService } from "../lib/website-service";
import {
	type GeneratedWebsite,
	WebsiteFeature,
} from "../utils/website-generator";

interface WebsiteSettingsModalProps {
	website: GeneratedWebsite;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onWebsiteUpdate: (website: GeneratedWebsite) => void;
}

export function WebsiteSettingsModal({
	website,
	open,
	onOpenChange,
	onWebsiteUpdate,
}: WebsiteSettingsModalProps) {
	const [saving, setSaving] = useState(false);
	const [config, setConfig] = useState(website.config);

	const handleSave = async () => {
		setSaving(true);
		try {
			const updatedWebsite = await WebsiteService.updateWebsiteConfig(
				website.id,
				config,
			);
			if (updatedWebsite) {
				onWebsiteUpdate(updatedWebsite);
				onOpenChange(false);
			}
		} catch (error) {
			console.error("Failed to update website:", error);
		} finally {
			setSaving(false);
		}
	};

	const toggleFeature = (feature: WebsiteFeature) => {
		setConfig((prev) => ({
			...prev,
			features: prev.features.includes(feature)
				? prev.features.filter((f) => f !== feature)
				: [...prev.features, feature],
		}));
	};

	const templateInfo = WebsiteService.getTemplateInfo(config.template);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-5xl w-full h-[85vh] flex flex-col p-0 gap-0">
				{/* Fixed Header */}
				<div className="flex items-center justify-between p-6 border-b border-border">
					<div>
						<DialogTitle className="text-lg font-semibold">
							Customize Your Website
						</DialogTitle>
						<DialogDescription className="text-sm text-muted-foreground mt-1">
							Make changes to how your website looks and what
							features it has
						</DialogDescription>
					</div>
					<div className="flex items-center space-x-2">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
						>
							Close Without Saving
						</Button>
						<Button onClick={handleSave} disabled={saving}>
							{saving ? (
								<>
									<SaveIcon className="h-4 w-4 mr-2 animate-spin" />
									Saving Your Changes...
								</>
							) : (
								<>
									<SaveIcon className="h-4 w-4 mr-2" />
									Save My Changes
								</>
							)}
						</Button>
					</div>
				</div>

				{/* Fixed Tabs Navigation */}
				<Tabs
					defaultValue="general"
					className="flex flex-col flex-1 min-h-0"
				>
					<div className="px-6 pt-4 border-b border-border">
						<TabsList className="grid w-full grid-cols-4 bg-muted/30">
							<TabsTrigger value="general" className="text-sm">
								📋 Basic Info
							</TabsTrigger>
							<TabsTrigger value="design" className="text-sm">
								🎨 Look & Feel
							</TabsTrigger>
							<TabsTrigger value="features" className="text-sm">
								⚡ What It Can Do
							</TabsTrigger>
							<TabsTrigger value="preview" className="text-sm">
								👀 See Your Website
							</TabsTrigger>
						</TabsList>
					</div>

					{/* Fixed Content Area with Scroll */}
					<div className="flex-1 overflow-y-auto">
						<div className="p-6 min-h-full">
							{/* General Tab */}
							<TabsContent
								value="general"
								className="space-y-6 mt-0"
							>
								<div className="space-y-6">
									<div>
										<h3 className="text-base font-medium mb-2">
											📋 About Your Website
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Here's the basic information about
											your business website
										</p>
										<div className="grid grid-cols-2 gap-4">
											<div className="space-y-2">
												<Label
													htmlFor="projectName"
													className="text-sm font-medium"
												>
													Website Type
												</Label>
												<Input
													id="projectName"
													value={templateInfo.name}
													disabled
													className="bg-muted/50"
												/>
												<p className="text-xs text-muted-foreground">
													This is the style of website
													we created for your business
												</p>
											</div>
											<div className="space-y-2">
												<Label
													htmlFor="template"
													className="text-sm font-medium"
												>
													Design Template
												</Label>
												<Input
													id="template"
													value={templateInfo.name}
													disabled
													className="bg-muted/50"
												/>
												<p className="text-xs text-muted-foreground">
													The design theme your
													website is using
												</p>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-2">
											🌐 Your Website Address
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											This is where people can find your
											website on the internet
										</p>
										<div className="space-y-4">
											<div className="p-4 bg-muted/30 rounded-lg border">
												<div className="flex items-center justify-between">
													<div>
														<p className="font-medium text-sm">
															Your Website Address
														</p>
														<p className="text-sm text-muted-foreground font-mono">
															{website.url
																.replace(
																	"https://",
																	"",
																)
																.replace(
																	"http://",
																	"",
																)}
														</p>
														<p className="text-xs text-muted-foreground mt-1">
															This is the web
															address customers
															use to visit your
															site
														</p>
													</div>
													<Badge status="success">
														✅ Working
													</Badge>
												</div>
											</div>
										</div>
										<div className="grid grid-cols-2 gap-4 mt-4">
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													🔒 Security Certificate
												</p>
												<p className="text-sm font-medium text-green-600">
													✅ Secure & Protected
												</p>
												<p className="text-xs text-muted-foreground mt-1">
													Your website is safe for
													visitors
												</p>
											</div>
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													⚡ Speed Optimization
												</p>
												<p className="text-sm font-medium text-green-600">
													✅ Fast Loading
												</p>
												<p className="text-xs text-muted-foreground mt-1">
													Your website loads quickly
													worldwide
												</p>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-2">
											📊 Website Status
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Here's how your website is
											performing right now
										</p>
										<div className="grid grid-cols-3 gap-4">
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													🟢 Current Status
												</p>
												<p className="text-sm font-medium">
													{website.status === "active"
														? "✅ Live & Working"
														: website.status ===
																"generating"
															? "⏳ Setting Up..."
															: "⚠️ Needs Attention"}
												</p>
												<p className="text-xs text-muted-foreground mt-1">
													Your website's current
													condition
												</p>
											</div>
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													📅 Last Updated
												</p>
												<p className="text-sm font-medium">
													{new Date(
														website.updatedAt,
													).toLocaleDateString(
														"en-US",
														{
															month: "short",
															day: "numeric",
														},
													)}
												</p>
												<p className="text-xs text-muted-foreground mt-1">
													When we last made changes
												</p>
											</div>
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													⚡ Setup Time
												</p>
												<p className="text-sm font-medium">
													~2 minutes
												</p>
												<p className="text-xs text-muted-foreground mt-1">
													How long updates usually
													take
												</p>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-2">
											⚙️ Technical Settings
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Behind-the-scenes settings that keep
											your website running smoothly
										</p>
										<div className="p-4 bg-muted/30 rounded-lg border">
											<div className="flex items-center justify-between">
												<div>
													<p className="text-sm font-medium">
														🔧 Website Configuration
													</p>
													<p className="text-xs text-muted-foreground">
														All technical settings
														are automatically
														managed for you
													</p>
												</div>
												<Button
													variant="outline"
													size="sm"
													disabled
												>
													✅ Auto-Managed
												</Button>
											</div>
										</div>
									</div>
								</div>
							</TabsContent>

							{/* Design Tab */}
							<TabsContent
								value="design"
								className="space-y-6 mt-0"
							>
								<div className="space-y-6">
									<div>
										<h3 className="text-base font-medium mb-2">
											🎨 Your Website Design
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											This is the design style your
											website is currently using
										</p>
										<div className="p-4 border rounded-lg bg-card">
											<div className="flex items-center gap-4">
												<div
													className="w-12 h-12 rounded-lg"
													style={{
														backgroundColor:
															templateInfo.color,
													}}
												/>
												<div className="flex-1">
													<h4 className="font-medium">
														{templateInfo.name}{" "}
														Design
													</h4>
													<p className="text-sm text-muted-foreground">
														{
															templateInfo.description
														}
													</p>
												</div>
												<Badge status="success">
													✅ Currently Using
												</Badge>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-2">
											🎨 Choose Your Colors
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Pick colors that match your business
											brand. These will appear throughout
											your website.
										</p>
										<div className="grid grid-cols-2 gap-4">
											<div className="space-y-2">
												<Label
													htmlFor="primaryColor"
													className="text-sm font-medium"
												>
													🎯 Main Brand Color
												</Label>
												<Input
													id="primaryColor"
													type="color"
													value={config.primaryColor}
													onChange={(e) =>
														setConfig((prev) => ({
															...prev,
															primaryColor:
																e.target.value,
														}))
													}
												/>
												<p className="text-xs text-muted-foreground">
													This color will be used for
													buttons, links, and
													highlights
												</p>
											</div>
											<div className="space-y-2">
												<Label
													htmlFor="secondaryColor"
													className="text-sm font-medium"
												>
													🎨 Accent Color
												</Label>
												<Input
													id="secondaryColor"
													type="color"
													value={
														config.secondaryColor
													}
													onChange={(e) =>
														setConfig((prev) => ({
															...prev,
															secondaryColor:
																e.target.value,
														}))
													}
												/>
												<p className="text-xs text-muted-foreground">
													This color will complement
													your main brand color
												</p>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-2">
											📝 Choose Your Font Style
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Pick a font that represents your
											business personality. This affects
											all text on your website.
										</p>
										<div className="space-y-4">
											<div className="space-y-2">
												<Label
													htmlFor="fontFamily"
													className="text-sm font-medium"
												>
													✍️ Text Style
												</Label>
												<select
													id="fontFamily"
													value={config.fontFamily}
													onChange={(e) =>
														setConfig((prev) => ({
															...prev,
															fontFamily:
																e.target.value,
														}))
													}
													className="w-full p-2 border border-border rounded-md bg-background"
												>
													<option value="Inter">
														Inter - Modern & Clean
													</option>
													<option value="Roboto">
														Roboto - Friendly &
														Professional
													</option>
													<option value="Open Sans">
														Open Sans - Easy to Read
													</option>
													<option value="Lato">
														Lato - Elegant & Warm
													</option>
												</select>
												<p className="text-xs text-muted-foreground">
													Choose the style that best
													fits your business image
												</p>
											</div>
											<div className="p-3 bg-muted/30 rounded-lg border">
												<p className="text-sm text-muted-foreground mb-2">
													👀 How it looks:
												</p>
												<p
													className="font-medium text-lg"
													style={{
														fontFamily:
															config.fontFamily,
													}}
												>
													Welcome to Your Business
												</p>
												<p
													className="text-sm mt-1"
													style={{
														fontFamily:
															config.fontFamily,
													}}
												>
													This is how your website
													text will appear to
													customers
												</p>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-2">
											🏗️ Choose Your Layout Style
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Pick how you want your website to be
											organized and look to visitors
										</p>
										<div className="grid grid-cols-3 gap-3">
											{[
												{
													key: "modern",
													name: "Modern",
													desc: "Clean & Contemporary",
												},
												{
													key: "classic",
													name: "Classic",
													desc: "Traditional & Professional",
												},
												{
													key: "minimal",
													name: "Minimal",
													desc: "Simple & Elegant",
												},
											].map((layout) => (
												<button
													key={layout.key}
													type="button"
													className={`p-3 border rounded-lg cursor-pointer transition-colors text-left ${
														config.layout ===
														layout.key
															? "border-primary bg-primary/5"
															: "border-border hover:border-primary/50"
													}`}
													onClick={() =>
														setConfig((prev) => ({
															...prev,
															layout: layout.key as any,
														}))
													}
												>
													<div className="text-center">
														<div
															className={`w-full h-12 rounded mb-2 ${
																layout.key ===
																"modern"
																	? "bg-gradient-to-r from-blue-500 to-purple-500"
																	: layout.key ===
																			"classic"
																		? "bg-gradient-to-r from-amber-500 to-orange-500"
																		: "bg-gradient-to-r from-gray-400 to-gray-600"
															}`}
														/>
														<p className="text-xs font-medium">
															{layout.name}
														</p>
														<p className="text-xs text-muted-foreground">
															{layout.desc}
														</p>
													</div>
												</button>
											))}
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-2">
											🎨 Your Color Scheme Preview
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Here's how all your chosen colors
											work together on your website
										</p>
										<div className="grid grid-cols-5 gap-3">
											{[
												{
													name: "Main Brand",
													color: config.primaryColor,
													desc: "Buttons & Links",
												},
												{
													name: "Accent",
													color: config.secondaryColor,
													desc: "Highlights",
												},
												{
													name: "Background",
													color: "#ffffff",
													desc: "Page Background",
												},
												{
													name: "Text",
													color: "#000000",
													desc: "Written Content",
												},
												{
													name: "Theme",
													color: templateInfo.color,
													desc: "Design Elements",
												},
											].map((item) => (
												<div
													key={item.name}
													className="text-center"
												>
													<div
														className="w-full h-12 rounded-lg border border-border mb-2"
														style={{
															backgroundColor:
																item.color,
														}}
													/>
													<p className="text-xs font-medium">
														{item.name}
													</p>
													<p className="text-xs text-muted-foreground">
														{item.desc}
													</p>
												</div>
											))}
										</div>
									</div>
								</div>
							</TabsContent>

							{/* Features Tab */}
							<TabsContent
								value="features"
								className="space-y-6 mt-0"
							>
								<div className="space-y-6">
									<div>
										<h3 className="text-base font-medium mb-2">
											⚡ What Your Website Can Do
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Turn features on or off to customize
											what your website offers to
											customers. You can change these
											anytime!
										</p>
										<div className="space-y-4">
											{Object.values(WebsiteFeature).map(
												(feature) => {
													const featureInfo = {
														[WebsiteFeature.CONTACT_FORM]:
															{
																icon: "📧",
																title: "Contact Form",
																desc: "Let customers send you messages directly from your website",
															},
														[WebsiteFeature.ONLINE_BOOKING]:
															{
																icon: "📅",
																title: "Online Booking",
																desc: "Allow customers to book appointments or services online",
															},
														[WebsiteFeature.GALLERY]:
															{
																icon: "🖼️",
																title: "Photo Gallery",
																desc: "Show off your work, products, or business with beautiful photos",
															},
														[WebsiteFeature.MENU]: {
															icon: "📋",
															title: "Menu/Services List",
															desc: "Display what you offer with prices and descriptions",
														},
														[WebsiteFeature.TESTIMONIALS]:
															{
																icon: "⭐",
																title: "Customer Reviews",
																desc: "Share what happy customers say about your business",
															},
														[WebsiteFeature.LOCATION_MAP]:
															{
																icon: "📍",
																title: "Location Map",
																desc: "Help customers find your business with an interactive map",
															},
														[WebsiteFeature.SOCIAL_LINKS]:
															{
																icon: "📱",
																title: "Social Media Links",
																desc: "Connect your Facebook, Instagram, and other social accounts",
															},
														[WebsiteFeature.BUSINESS_HOURS]:
															{
																icon: "🕒",
																title: "Business Hours",
																desc: "Show when you're open so customers know when to visit",
															},
														[WebsiteFeature.SERVICES_LIST]:
															{
																icon: "🛠️",
																title: "Services & Products",
																desc: "List everything you offer to help customers understand your business",
															},
														[WebsiteFeature.TEAM_SECTION]:
															{
																icon: "👥",
																title: "Meet the Team",
																desc: "Introduce yourself and your team to build trust with customers",
															},
													}[feature];

													return (
														<div
															key={feature}
															className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/30 transition-colors"
														>
															<div className="flex items-start gap-3">
																<span className="text-2xl">
																	{
																		featureInfo?.icon
																	}
																</span>
																<div>
																	<p className="font-medium">
																		{
																			featureInfo?.title
																		}
																	</p>
																	<p className="text-sm text-muted-foreground">
																		{
																			featureInfo?.desc
																		}
																	</p>
																</div>
															</div>
															<div className="flex items-center gap-2">
																<span className="text-xs text-muted-foreground">
																	{config.features.includes(
																		feature,
																	)
																		? "ON"
																		: "OFF"}
																</span>
																<Switch
																	checked={config.features.includes(
																		feature,
																	)}
																	onCheckedChange={() =>
																		toggleFeature(
																			feature,
																		)
																	}
																/>
															</div>
														</div>
													);
												},
											)}
										</div>
									</div>
								</div>
							</TabsContent>

							{/* Preview Tab */}
							<TabsContent
								value="preview"
								className="space-y-6 mt-0"
							>
								<div className="space-y-6">
									<div>
										<h3 className="text-base font-medium mb-2">
											👀 See Your Website
										</h3>
										<p className="text-sm text-muted-foreground mb-4">
											Take a look at how your website
											appears to customers with all your
											changes applied
										</p>
										<div className="border rounded-lg bg-muted/30 p-4">
											<div className="flex items-center justify-between mb-4">
												<p className="text-sm text-muted-foreground">
													🌐 Your live website is
													ready to view
												</p>
												<Button
													size="sm"
													asChild
													className="gap-2"
												>
													<a
														href={website.url}
														target="_blank"
														rel="noopener noreferrer"
													>
														<ExternalLinkIcon className="h-3 w-3" />
														🚀 Open My Website
													</a>
												</Button>
											</div>
											<div className="aspect-video bg-background border rounded-md flex items-center justify-center">
												<div className="text-center space-y-2">
													<GlobeIcon className="h-12 w-12 text-primary mx-auto mb-2" />
													<h4 className="font-medium">
														Your Website is Live!
													</h4>
													<p className="text-sm text-muted-foreground max-w-md">
														Click the button above
														to see exactly how your
														website looks to
														customers. It will open
														in a new tab so you can
														compare it with your
														settings here.
													</p>
													<p className="text-xs text-muted-foreground mt-2">
														💡 Tip: Any changes you
														make here will update
														your live website
													</p>
												</div>
											</div>
										</div>
									</div>
								</div>
							</TabsContent>
						</div>
					</div>
				</Tabs>
			</DialogContent>
		</Dialog>
	);
}
